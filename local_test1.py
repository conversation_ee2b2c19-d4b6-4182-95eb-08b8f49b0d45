import numpy as np
import matplotlib.pyplot as plt
from matplotlib.ticker import FuncFormatter

# ==============================================================================
# 公共设置 (与您的代码风格保持一致)
# ==============================================================================
# 设置matplotlib支持中文显示
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False    # 用来正常显示负号
plt.rcParams['figure.dpi'] = 150 # 提高图像分辨率
# 增大默认字体大小
plt.rcParams['font.size'] = 14
plt.rcParams['axes.titlesize'] = 18
plt.rcParams['axes.labelsize'] = 16
plt.rcParams['xtick.labelsize'] = 14
plt.rcParams['ytick.labelsize'] = 14
plt.rcParams['legend.fontsize'] = 14

# ==============================================================================
# 图1: 验证混合输入结构
# ==============================================================================
def plot_hybrid_input_validation():
    """
    绘制条形图, 验证混合输入结构的优越性。
    """
    # --- 模拟数据 ---
    # TODO: 请用您的真实实验结果替换这里的模拟数据
    # models = ['仅物理观测', '仅LS初步解', '混合输入模型 (本文)']
    # errors = [12.8, 15.3, 4.2]  # 对应的平均定位误差 (RMSE, 单位: 米)
    models = ['仅物理观测', '仅LS初步解', '混合输入(本文)']
    errors = [np.random.uniform(12, 14), np.random.uniform(15, 17), np.random.uniform(3.5, 5.0)]
    errors = [round(e, 1) for e in errors]
    colors = ['#1E88E5', '#FF8F00', '#D81B60']

    fig, ax = plt.subplots(figsize=(10, 7))

    bars = ax.bar(models, errors, color=colors, width=0.6)

    # 在条形图上添加数值标签
    for bar in bars:
        yval = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2.0, yval, f'{yval} m', va='bottom', ha='center', fontsize=16, fontweight='bold')

    ax.set_ylabel('平均定位误差 (RMSE, 米)', fontsize=18)
    ax.set_title('不同输入结构对定位性能的影响', fontsize=20, fontweight='bold')
    ax.spines['top'].set_visible(False)
    ax.spines['right'].set_visible(False)
    ax.yaxis.grid(True, linestyle='--', which='major', color='grey', alpha=.5)
    ax.set_ylim(0, max(errors) * 1.2)
    plt.xticks(fontsize=16)
    plt.yticks(fontsize=16)

    plt.tight_layout()
    plt.savefig('plot1_hybrid_input_validation.png', dpi=300, bbox_inches='tight')
    plt.show()

# ==============================================================================
# 图2: 验证鲁棒训练机制
# ==============================================================================
def plot_robustness_validation():
    """
    绘制线图, 验证鲁棒训练机制在噪声环境下的有效性。
    """
    # --- 模拟数据 ---
    # TODO: 请用您的真实实验结果替换这里的模拟数据
    # noise_levels = [0.0, 0.1, 0.2, 0.3, 0.4, 0.5] # 输入数据的噪声标准差
    # standard_model_errors = [8.5, 15.3, 25.1, 40.5, 60.1, 85.3] # 标准模型的误差
    # robust_model_errors = [4.2, 6.8, 9.5, 13.2, 18.5, 25.0]     # 鲁棒模型的误差
    noise_levels = np.linspace(0, 0.5, 6)
    base_robust_error = np.random.uniform(3.5, 5.0)
    base_standard_error = base_robust_error * np.random.uniform(1.6, 2.0)
    
    robust_model_errors = base_robust_error + noise_levels * 20 + (noise_levels**2) * 50 + np.random.normal(0, 1, len(noise_levels))
    standard_model_errors = base_standard_error + noise_levels * 50 + (noise_levels**2) * 150 + np.random.normal(0, 2, len(noise_levels))


    fig, ax = plt.subplots(figsize=(10, 7))

    ax.plot(noise_levels, robust_model_errors, 'o-', label='鲁棒模型 (本文)', color='#D81B60', linewidth=3, markersize=10)
    ax.plot(noise_levels, standard_model_errors, 's--', label='标准模型', color='#1E88E5', linewidth=3, markersize=10)

    ax.set_xlabel('注入噪声水平 (归一化标准差)', fontsize=18)
    ax.set_ylabel('平均定位误差 (RMSE, 米)', fontsize=18)
    ax.set_title('模型在不同噪声水平下的性能表现', fontsize=20, fontweight='bold')
    ax.legend(fontsize=16)
    ax.grid(True, linestyle='--', which='major', color='grey', alpha=.5)
    
    ax.spines['top'].set_visible(False)
    ax.spines['right'].set_visible(False)
    plt.xticks(fontsize=16)
    plt.yticks(fontsize=16)
    
    plt.tight_layout()
    plt.savefig('plot2_robustness_validation.png', dpi=300, bbox_inches='tight')
    plt.show()

# ==============================================================================
# 图3: 验证多任务输出 (置信度评估)
# ==============================================================================
def plot_confidence_validation():
    """
    绘制散点/Hexbin图, 验证置信度分数与真实误差的关联。
    """
    # --- 模拟数据 ---
    # TODO: 请用您的真实实验结果替换这里的模拟数据
    # num_samples = 500
    # confidence_scores = np.random.rand(num_samples)**0.5  # 模拟置信度，使其偏向于高分
    # true_errors = (1 - confidence_scores + np.random.rand(num_samples) * 0.2) * 30 + np.random.rand(num_samples) * 5
    # true_errors = np.maximum(true_errors, 0) # 误差不能为负
    
    num_samples = 500
    confidence_scores = np.random.beta(10, 1.5, size=num_samples)  # 更真实的置信度分布
    base_error_for_conf = np.linspace(50, 2, num_samples)
    sorted_confidence = np.sort(confidence_scores)
    
    # 创建误差，使其与置信度强相关
    true_errors = base_error_for_conf + (1-sorted_confidence)*np.random.uniform(0,10, size=num_samples)
    true_errors = true_errors - (true_errors.min()) + 1 # 确保最小误差大于0
    
    # 随机打乱以匹配原始未排序的置信度
    indices = np.random.permutation(len(true_errors))
    confidence_scores, true_errors = sorted_confidence[indices], true_errors[indices]


    fig, ax = plt.subplots(figsize=(10, 7))

    # 使用 Hexbin 图更适合大量数据点
    hb = ax.hexbin(confidence_scores, true_errors, gridsize=30, cmap='viridis_r', mincnt=1, alpha=0.9)
    
    # 添加颜色条
    cb = fig.colorbar(hb, ax=ax, label='数据点密度')
    cb.set_label('样本点密度', size=16)

    # 绘制趋势线
    z = np.polyfit(confidence_scores, true_errors, 1)
    p = np.poly1d(z)
    x_trend = np.linspace(ax.get_xlim()[0], ax.get_xlim()[1], 100)
    ax.plot(x_trend, p(x_trend), "r--", linewidth=3, label='拟合趋势线')


    ax.set_xlabel('模型预测置信度', fontsize=18)
    ax.set_ylabel('真实定位误差 (米)', fontsize=18)
    ax.set_title('模型置信度与定位误差关系验证', fontsize=20, fontweight='bold')
    ax.legend(fontsize=16)
    ax.set_xlim(min(confidence_scores)-0.05, 1.05)
    ax.set_ylim(0, max(true_errors)*1.1)
    
    plt.xticks(fontsize=16)
    plt.yticks(fontsize=16)
    
    plt.tight_layout()
    plt.savefig('plot3_confidence_validation.png', dpi=300, bbox_inches='tight')
    plt.show()

# ==============================================================================
# 运行并生成所有图像
# ==============================================================================
if __name__ == '__main__':
    print("正在生成图1: 验证混合输入结构...")
    plot_hybrid_input_validation()

    print("\n正在生成图2: 验证鲁棒训练机制...")
    plot_robustness_validation()

    print("\n正在生成图3: 验证多任务输出 (置信度评估)...")
    plot_confidence_validation()