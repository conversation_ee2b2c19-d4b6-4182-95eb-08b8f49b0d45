import numpy as np
import matplotlib.pyplot as plt
from matplotlib.ticker import FuncFormatter

# ==============================================================================
# 公共设置 (与您的代码风格保持一致)
# ==============================================================================
# 设置matplotlib支持中文显示
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False    # 用来正常显示负号
plt.rcParams['figure.dpi'] = 150 # 提高图像分辨率
# 增大默认字体大小
plt.rcParams['font.size'] = 14
plt.rcParams['axes.titlesize'] = 18
plt.rcParams['axes.labelsize'] = 16
plt.rcParams['xtick.labelsize'] = 14
plt.rcParams['ytick.labelsize'] = 14
plt.rcParams['legend.fontsize'] = 14

# ==============================================================================
# 图1: 不同场景下传统方法与神经网络性能对比
# ==============================================================================
def plot_method_comparison_under_scenarios():
    """
    绘制分组柱状图和折线图，在不同参数下对比传统方法与神经网络方法的性能。
    """
    # --- 扩展并修正模拟数据以呈现更真实的效果 ---
    # 定义五种不同的测试场景
    scenarios = [
        '场景 A\n(SNR=12dB, 高动态)', 
        '场景 B\n(SNR=15dB, 中动态)', 
        '场景 C\n(SNR=18dB, 低动态)',
        '场景 D\n(SNR=20dB, 极低动态)',
        '场景 E\n(SNR=25dB, 静态)'
    ]
    
    # 传统方法的误差（米）- 随着条件变好，误差降低
    traditional_errors = [152.3, 115.1, 88.4, 74.9, 55.2]
    # 神经网络方法的误差（米）- 同样降低，但与传统方法的差距会缩小
    nn_errors = [98.5, 80.5, 66.3, 63.7, 51.9]
    
    # 计算性能提升百分比 - 现在这个趋势会是下降的，更符合实际
    improvement_percentage = (np.array(traditional_errors) - np.array(nn_errors)) / np.array(traditional_errors) * 100

    x = np.arange(len(scenarios))  # 标签位置
    width = 0.35  # 柱子宽度

    # --- 创建图表 (已修改figsize以匹配更接近正方形的比例) ---
    fig, ax = plt.subplots(figsize=(12, 9)) # 修改长宽比
    fig.patch.set_facecolor('#f8f9fa') # 设置图表背景色
    ax.set_facecolor('#f8f9fa')

    # --- 绘制柱状图 (左Y轴) ---
    rects1 = ax.bar(x - width/2, traditional_errors, width, label='传统方法误差', color='#4c72b0', zorder=2)
    rects2 = ax.bar(x + width/2, nn_errors, width, label='神经网络方法误差', color='#c44e52', zorder=2)

    # 在每个柱子上方添加精确的数值标签
    ax.bar_label(rects1, padding=3, fmt='%.1f m', fontsize=11, fontweight='bold', color='#333333')
    ax.bar_label(rects2, padding=3, fmt='%.1f m', fontsize=11, fontweight='bold', color='#333333')

    # 设置左Y轴的标签和范围
    ax.set_ylabel('平均定位误差 (RMSE, 米)', fontsize=18, fontweight='bold')
    ax.set_ylim(0, max(traditional_errors) * 1.25)

    # --- 创建并绘制折线图 (右Y轴) ---
    ax2 = ax.twinx()  # 创建共享X轴的第二个Y轴
    line = ax2.plot(x, improvement_percentage, 'o-', color='#55a868', linewidth=3, markersize=10, label='性能提升率')

    # 设置右Y轴的标签和格式
    ax2.set_ylabel('性能提升率 (%)', fontsize=18, fontweight='bold', color='#55a868')
    ax2.tick_params(axis='y', labelcolor='#55a868')
    ax2.set_ylim(0, max(improvement_percentage) * 1.3)
    ax2.yaxis.set_major_formatter(FuncFormatter(lambda y, _: f'{y:.0f}%'))

    # --- 统一设置和图例 ---
    ax.set_title('不同场景下传统方法与神经网络性能对比', fontsize=22, fontweight='bold', pad=20)
    ax.set_xticks(x)
    ax.set_xticklabels(scenarios, fontsize=14, linespacing=1.5) # 微调字体以适应新尺寸
    
    # 合并两个Y轴的图例并移动到右上角
    lines, labels = ax.get_legend_handles_labels()
    lines2, labels2 = ax2.get_legend_handles_labels()
    ax.legend(lines + lines2, labels + labels2, loc='upper right', fontsize=14, frameon=True, shadow=True, borderpad=1, title='图例', title_fontsize='16')

    # 优化图表外观
    ax.spines['top'].set_visible(False)
    ax.spines['right'].set_visible(False)
    ax.spines['left'].set_color('grey')
    ax.spines['bottom'].set_color('grey')
    ax.yaxis.grid(True, linestyle='--', which='major', color='grey', alpha=.25)
    
    fig.tight_layout()
    # --- 已修改保存格式为PNG ---
    plt.savefig('plot_method_comparison_combined.png', format='png', dpi=300, bbox_inches='tight', facecolor=fig.get_facecolor())
    plt.show()


# ==============================================================================
# 运行并生成图像
# ==============================================================================
if __name__ == '__main__':
    print("正在生成方法性能对比图 (PNG格式)...")
    plot_method_comparison_under_scenarios()
    print("图像已保存为 plot_method_comparison_combined.png")
